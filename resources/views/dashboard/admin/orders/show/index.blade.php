@extends('layouts.dashboard')

@section('title', ' - ویرایش اطلاعات')

@section('content')
    {{-- <div class="bg-gray-800 w-full relative left-0 top-0 h-60"></div> --}}
    <div x-data="{
        items: 1000,
        showImageModal: false,
        showProductDetail: false,
        imageUrl: '',
        setImage(url) {
            this.imageUrl = url;
            this.showImageModal = true;
        },

    }">
        <main class="container relative mx-auto mt-6 pb-20 md:mt-0 md:pt-6">
            @if (auth()->user()->level == 'admin')
                <div class="mb-3 flex items-center gap-3 overflow-x-auto px-3 md:px-0">
                    @if (auth()->user()->level == 'admin')
                        <button
                            class="rounded-lg bg-red-500 px-4 py-2 text-white transition-all hover:bg-red-600"
                            type="button"
                            @click="createFactorModal = true, lock = true"
                        >
                            <div class="flex items-center gap-2">
                                <svg
                                    class="h-5 w-5"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                    />
                                </svg>
                                <span class="whitespace-nowrap text-sm">فاکتور سفارش</span>
                            </div>
                        </button>
                    @endif
                    {{-- <livewire:dashboard.admin.orders.send-sms :orderId="$order->id" /> --}}
                    <a
                        class="rounded-lg bg-gray-100 px-4 py-2 text-gray-700 transition-all hover:bg-gray-200"
                        href="{{ route('admin-dashboard-print-order', $order->id) }}"
                        target="_blank"
                    >
                        <div class="flex items-center gap-2">
                            <svg
                                class="h-5 w-5"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
                                />
                            </svg>
                            <span class="whitespace-nowrap text-sm">چاپ آدرس</span>
                        </div>
                    </a>
                    <a
                        class="rounded-lg bg-gray-100 px-4 py-2 text-gray-700 transition-all hover:bg-gray-200"
                        href="{{ route('admin-dashboard-factor-print-order', ['orderId' => $order->id, 'auto' => 'true']) }}"
                        target="_blank"
                    >
                        <div class="flex items-center gap-2">
                            <svg
                                class="h-5 w-5"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"
                                />
                            </svg>
                            <span class="whitespace-nowrap text-sm">چاپ فاکتور</span>
                        </div>
                    </a>

                </div>
            @endif

            <div
                class="@if (auth()->user()->level == 'admin') grid md:grid-cols-12 grid-cols-1 gap-3 @else w-full max-w-5xl mx-auto md:px-0 px-3 @endif">
                <div class="w-full px-3 max-md:px-0 md:col-span-8 md:px-0">

                    @if (auth()->user()->level != 'admin')
                        @include('layouts.dashboard.breadcrumb', ['category' => $order->type])
                    @endif

                    @include('layouts.dashboard.order-box-creator', [
                        'category' => $order->type,
                        'user' => $order->user,
                        'reserve' => $order?->invoice->order_status ?? 'حضوری',
                    ])

                    {{-- @if (isset($transaction) && $transaction != null && $transaction->result == 100)
                        <div class="mt-2 rounded-lg bg-white p-3 shadow">
                            <div style="">
                                <div style="display: flex; align-items: center;">
                                    <span
                                        style="color: white; background-color: #22c55e; border-radius: 9999px; padding: 0.5rem; display: flex; align-items: center; justify-content: center; width: 2rem; height: 2rem; margin-left: 10px;"
                                    >
                                        <svg
                                            style="width: 24px; height: 24px; flex-shrink: 0;"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="m4.5 12.75 6 6 9-13.5"
                                            />
                                        </svg>
                                    </span>
                                    <p style="font-weight: 800; margin: 0; padding: 0; color: green;">مبلغ این سفارش بصورت
                                        آنلاین پرداخت شده</p>
                                </div>
                                <p class="mt-2 text-base">شناسه پرداخت: <span>{{ $transaction->trackId }}</span></p>
                            </div>
                        </div>
                    @endif --}}

                    <livewire:dashboard.admin.order.show
                        :order="$order"
                        :settings="$settings"
                    />
                </div>
                @if (auth()->user()->level == 'admin')
                    <div class="w-full px-3 md:col-span-4 md:px-0">

                        <livewire:dashboard.admin.order.financial
                            :order="$order"
                            :settings="$settings"
                        />
                        @if ($order->survey != null)
                            <div class="mt-6 max-md:hidden">
                                <div class="h-full overflow-y-auto rounded-xl bg-white shadow-md">
                                    <livewire:dashboard.admin.orders.survey.show-survey-modal :order="$order" />
                                </div>

                            </div>
                        @endif
                    </div>
                @endif
            </div>

        </main>
        <div
            class="fixed left-0 top-0 z-[1003] flex h-screen w-screen items-start justify-center backdrop-blur-sm md:h-full md:w-full"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="getSubscribeModal"
            x-cloak
        >
            <div
                class="fade-scale relative top-10 h-auto w-full max-w-2xl rounded-xl bg-gray-900 p-5 shadow-xl"
                x-show="getSubscribeModal"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 scale-75"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-75"
            >
                <div>
                    <div class="mb-3 flex justify-between p-3 pb-0 pt-0 dark:border-gray-800">
                        <div>
                            <h1 class="text-base text-gray-200 md:text-xl">جستجو در بین مشتریان</h1>
                        </div>
                        <button
                            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="getSubscribeModal = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <livewire:dashboard.admin.subscribers.find-user-modal lazy="on-load" />

                </div>
            </div>
        </div>
        @if (auth()->user()->level == 'admin')
            <div
                class="fixed left-0 top-0 z-[1001] flex h-[calc(100%-1rem)] max-h-full items-start justify-center overflow-y-auto overflow-x-hidden bg-gray-900/80 md:inset-0 md:h-screen md:w-full"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="transition ease-in duration-300"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                x-show="createFactorModal"
                x-cloak
            >
                <div
                    class="nav-fade-in relative top-10 h-auto min-h-96 w-full"
                    x-show="createFactorModal"
                    x-transition:enter="transition ease-out duration-100"
                    x-transition:enter-start="opacity-0 scale-100"
                    x-transition:enter-end="opacity-100 scale-100"
                    x-transition:leave="transition ease-in duration-100"
                    x-transition:leave-start="opacity-100 scale-100"
                    x-transition:leave-end="opacity-0 scale-100"
                >
                    <div class="grid gap-6 px-10 md:grid-cols-12">
                        <div class="pb-32 md:col-span-9">
                            <div
                                class="rounded-xl bg-white p-5 shadow-xl dark:bg-gray-900"
                                x-data="{ lockButtonGold18k: false }"
                            >
                                <div class="mb-3 flex justify-between p-3 pb-0 pt-0 dark:border-gray-800">
                                    <div class="flex items-center justify-between gap-12">
                                        <div>
                                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200 md:text-xl">
                                                ایجاد
                                                فاکتور جدید</h1>
                                            <p class="text-sm text-gray-500 dark:text-gray-300">دقت بفرمایید این فاکتور
                                                برای
                                                این سفارش ایجاد یا ویرایش خواهد شد</p>
                                        </div>
                                        <div class="flex items-center gap-3">
                                            @if ($order->post_type == 'پست')
                                                <span class="rounded bg-red-500 p-2 md:px-8">
                                                    <span class="text-base text-white">نوع ارسال:
                                                        {{ $order->post_type }}</span>
                                                </span>
                                            @else
                                                <span class="rounded bg-gray-200 p-2 md:px-8">
                                                    <span class="text-base text-gray-700">نوع ارسال:
                                                        {{ $order->post_type }}</span>
                                                </span>
                                            @endif
                                            <span class="rounded bg-yellow-100 p-2 md:px-8">
                                                <span class="text-base text-gray-700">قیمت طلایی که کارشناس با مشتری بسته
                                                    است:
                                                    {{ formatMoney($order->gold18k) }}</span>
                                            </span>
                                            <button
                                                type="button"
                                                @click="lockButtonGold18k = !lockButtonGold18k"
                                            >
                                                <span class="px-2 py-1.5 text-gray-400 transition-all hover:text-red-500">
                                                    <svg
                                                        class="size-6"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke-width="1.5"
                                                        stroke="currentColor"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
                                                        />
                                                    </svg>

                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                    <button
                                        class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                                        type="button"
                                        @click="createFactorModal = false; lock = false"
                                    >
                                        <svg
                                            class="h-5 w-5"
                                            aria-hidden="true"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                clip-rule="evenodd"
                                            ></path>
                                        </svg>
                                        <span class="sr-only">Close modal</span>
                                    </button>
                                </div>
                                <livewire:dashboard.admin.order.factor.index
                                    :order="$order"
                                    lazy="on-load"
                                />
                            </div>
                        </div>
                        <div class="md:col-span-3">
                            <livewire:dashboard.admin.order.factor.details :order="$order" />

                        </div>
                    </div>
                </div>
            </div>
        @endif
        <div
            class="fixed left-0 top-0 z-[1001] flex h-[calc(100%-1rem)] max-h-full items-center justify-center overflow-y-auto overflow-x-hidden bg-gray-900/80 md:inset-0 md:h-screen md:w-full"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="historyModal"
            @click="historyModal = false; lock = false"
            x-cloak
        >
            <div
                class="relative top-10"
                x-show="historyModal"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="translate-y-full"
                x-transition:enter-end="translate-y-0"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="translate-y-0"
                x-transition:leave-end="translate-y-full"
            >
                <div class="mx-auto h-full w-auto max-w-3xl rounded-xl bg-white">
                    <div class="mb-3 flex justify-between p-3 dark:border-gray-800">
                        <div>
                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200">تاریخچه تغییر وضعیت
                            </h1>

                        </div>
                        <button
                            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="historyModal = false; lock = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="p-5 pt-0">
                        <livewire:dashboard.admin.orders.show.show-history-modal :histories="$order?->statusHistories" />
                    </div>
                </div>
            </div>
        </div>

        <div
            class="fixed bottom-0 left-0 right-0 top-0 z-[9999] h-full w-full bg-gray-900/80 md:py-2"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="showImageModal"
            x-cloak
        >
            <div
                class="fade-scale relative h-full w-full"
                x-show="showImageModal"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 scale-75"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-75"
            >
                <div
                    class="relative mx-auto h-full max-w-xl overflow-hidden bg-white md:rounded-xl"
                    @click.away="showImageModal = false; lock = false"
                >
                    <div class="mb-3 flex justify-between p-3 dark:border-gray-800">
                        <div>
                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200 md:text-xl">مشاهده عکس</h1>
                        </div>
                        <button
                            class="z-[99991] mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="showImageModal = false; lock = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="flex h-full items-center justify-center overflow-hidden">
                        <img
                            class="max-h-full max-w-full object-contain"
                            alt="تصویر"
                            :src="imageUrl"
                        >
                    </div>

                </div>

            </div>
        </div>

        <div
            class="fixed bottom-0 left-0 right-0 top-0 z-[1001] flex h-full w-full items-end justify-center bg-gray-900/80 md:items-center"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="showScreenShotModal"
            x-cloak
        >
            <div
                class="md:fade-scale relative flex w-screen items-end justify-center overflow-y-auto p-3 max-md:max-h-[90vh] md:h-screen md:items-center"
                x-show="showScreenShotModal"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 scale-75"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-75"
            >
                <div
                    class="relative mx-auto h-full w-full rounded-xl bg-white"
                    @click.away="showScreenShotModal = false; lock = false"
                >
                    <div class="mb-3 flex justify-between p-3 dark:border-gray-800">
                        <div>
                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200 md:text-xl">ضبط تصویر با وبکم
                            </h1>
                        </div>
                        <button
                            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="showScreenShotModal = false;  lock = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div
                        class="h-full space-y-4 overflow-hidden overflow-y-auto bg-white p-5"
                        x-data="{
                            stream: null,
                            isLoading: false,
                            startCamera() {
                                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                                    alert('مرورگر شما از دسترسی به دوربین پشتیبانی نمی‌کند یا صفحه در context امن بارگذاری نشده است.');
                                    return;
                                }

                                navigator.mediaDevices.getUserMedia({ video: true })
                                    .then((stream) => {
                                        this.stream = stream;
                                        this.$refs.video.srcObject = stream;
                                    })
                                    .catch((error) => {
                                        alert('دوربین فعال نشد: ' + error.message);
                                    });
                            },
                            takeSnapshot() {
                                const video = this.$refs.video;
                                const canvas = this.$refs.canvas;
                                const snapshot = this.$refs.snapshot;

                                canvas.width = video.videoWidth;
                                canvas.height = video.videoHeight;

                                const context = canvas.getContext('2d');
                                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                                const dataURL = canvas.toDataURL('image/png');

                                snapshot.src = dataURL;
                                snapshot.classList.remove('hidden');
                            },
                            resetSnapshot() {
                                const snapshot = this.$refs.snapshot;
                                snapshot.src = '';
                                snapshot.classList.add('hidden');
                            },
                            async sendToServer() {
                                try {
                                    this.isLoading = true; // شروع لودینگ
                                    const dataURL = this.$refs.canvas.toDataURL('image/png');

                                    Livewire.dispatch('captured', {
                                        image: dataURL,
                                        imageItem: screenShotItemNumber
                                    });


                                    const snapshot = this.$refs.snapshot;
                                    snapshot.src = '';
                                    snapshot.classList.add('hidden');

                                    showScreenShotModal = false;
                                    lock: false;
                                } catch (error) {
                                    console.error('خطا در ارسال تصویر:', error);
                                    this.$dispatch('notify', {
                                        type: 'error',
                                        message: 'خطا در ارسال تصویر به سرور'
                                    });
                                } finally {
                                    this.isLoading = false; // پایان لودینگ
                                }
                            },
                            zoom: 1,
                            capabilities: null,
                            track: null,
                            updateZoom(event) {
                                const newZoom = parseFloat(event.target.value);
                                this.zoom = newZoom;
                                if (this.track) {
                                    this.track.applyConstraints({
                                        advanced: [{ zoom: newZoom }]
                                    });
                                }
                            },
                        }"
                    >
                        <!-- پیش‌نمایش ویدیو -->
                        <div class="flex items-center justify-center gap-8">
                            <div class="relative h-[30rem] w-[32rem] rounded-none border">
                                <video
                                    class="h-full w-full"
                                    x-ref="video"
                                    autoplay
                                >
                                </video>
                                <span class="absolute right-2 top-2 rounded-md bg-red-500 px-2 py-1 text-white">دوربین
                                    آنلاین</span>

                            </div>

                            <!-- پیش‌نمایش عکس گرفته شده -->
                            <canvas
                                class="hidden h-[30rem] w-[32rem] rounded-none border-green-500"
                                x-ref="canvas"
                            >
                            </canvas>
                            <img
                                class="hidden h-[30rem] w-[32rem] rounded-none border-green-500"
                                x-ref="snapshot"
                            />
                        </div>

                        <!-- دکمه‌ها -->
                        <div class="mx-auto flex items-center justify-center gap-2 pb-3 pt-8">
                            <button
                                class="max-md:texts-xs rounded bg-gray-200 px-4 py-2 text-base text-gray-700 transition-all hover:bg-gray-300"
                                type="button"
                                @click="startCamera()"
                                :disabled="isLoading"
                            >
                                روشن کردن دوربین
                            </button>

                            <button
                                class="max-md:texts-xs rounded bg-gray-200 px-4 py-2 text-base text-gray-700 transition-all hover:bg-gray-300"
                                type="button"
                                @click="takeSnapshot()"
                                :disabled="isLoading"
                            >
                                گرفتن شات
                            </button>

                            <button
                                class="max-md:texts-xs rounded bg-green-500 px-4 py-2 text-base text-white transition-all hover:bg-green-600"
                                type="button"
                                @click="sendToServer()"
                                :disabled="isLoading"
                            >
                                <span x-show="!isLoading">ارسال به سرور</span>
                                <span
                                    class="flex items-center justify-center"
                                    x-show="isLoading"
                                >
                                    <svg
                                        class="-ml-1 mr-2 h-4 w-4 animate-spin text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            class="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            stroke-width="4"
                                        ></circle>
                                        <path
                                            class="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                        ></path>
                                    </svg>
                                    در حال ارسال...
                                </span>
                            </button>
                            <button
                                class="max-md:texts-xs rounded bg-gray-200 px-4 py-2 text-base text-gray-700 transition-all hover:bg-gray-300"
                                type="button"
                                @click="resetSnapshot()"
                                :disabled="isLoading"
                            >
                                خالی کردن
                            </button>
                        </div>
                        {{-- <template x-if="capabilities && capabilities.zoom"> --}}
                        <div class="flex flex-col items-center justify-center">
                            <p class="font-bol text-sm">نکته: قابلیت زوم تنها در برخی دستگاه‌ها و دوربین‌ها پشتیبانی
                                می‌شود. لطفاً بررسی کنید که وب‌کم یا دوربین مورد استفاده شما از این ویژگی پشتیبانی می‌کند یا
                                خیر.</p>
                            <div class="my-4 flex items-center gap-3">
                                <label class="text-sm font-semibold text-gray-700">زوم</label>
                                <input
                                    class="w-full"
                                    type="range"
                                    min="1"
                                    :max="capabilities.zoom.max"
                                    step="0.1"
                                    x-model="zoom"
                                    @input="updateZoom"
                                />
                                <span
                                    class="text-sm text-gray-500"
                                    x-text="zoom.toFixed(1)"
                                ></span>
                            </div>
                        </div>
                        {{-- </template> --}}
                        <!-- اسپینر لودینگ سراسری -->
                        <div
                            class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
                            x-show="isLoading"
                        >
                            <div class="flex items-center rounded-lg bg-white p-6 shadow-lg">
                                <svg
                                    class="mr-3 h-8 w-8 animate-spin text-purple-500"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <circle
                                        class="opacity-25"
                                        cx="12"
                                        cy="12"
                                        r="10"
                                        stroke="currentColor"
                                        stroke-width="4"
                                    ></circle>
                                    <path
                                        class="opacity-75"
                                        fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                </svg>
                                <span class="text-lg">در حال ذخیره تصویر...</span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        {{-- <div
            class="fixed bottom-0 left-0 right-0 top-0 z-[1001] h-full w-full bg-gray-900/80 py-2"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="showProductDetail"
            x-cloak
        >
            <div
                class="fade-scale relative h-full w-full"
                x-show="showProductDetail"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 scale-75"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-75"
            >
                <div class="relative mx-auto h-auto max-w-xl overflow-hidden rounded-xl bg-white">
                    <div class="mb-3 flex justify-between p-3 dark:border-gray-800">
                        <div>
                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200 md:text-xl">ویرایش جزییات محصول
                            </h1>
                        </div>
                        <button
                            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="showProductDetail = false; lock = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="flex h-full items-center justify-center">
                        <livewire:dashboard.admin.order.product-detail.product-detail-show />
                    </div>
                </div>

            </div>
        </div> --}}

    @stop

    @push('script')
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                document.getElementById("nav-bottom")?.remove();
            });

            function Comma(Num) {
                Num = Num.toString().replace(/,/g, '');
                if (isNaN(Num) || Num === '') {
                    return '';
                }
                let negative = Num[0] === '-' ? '-' : '';
                Num = Num.replace('-', '');
                let parts = Num.split('.');
                let integerPart = parts[0];
                let decimalPart = parts.length > 1 ? '.' + parts[1] : '';
                let rgx = /(\d+)(\d{3})/;
                while (rgx.test(integerPart)) {
                    integerPart = integerPart.replace(rgx, '$1' + ',' + '$2');
                }
                return negative + integerPart + decimalPart;
            }

            jalaliDatepicker.startWatch();
        </script>
    @endpush
